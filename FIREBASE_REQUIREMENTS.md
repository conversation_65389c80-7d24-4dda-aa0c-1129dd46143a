# Firebase Analytics Integration Requirements

Based on the Games24x7 Medium article, here are the additional requirements for proper Firebase Server-to-Server integration:

## 🔑 Key Differences from Current Implementation

### 1. **Different API Endpoint**
- **Current**: `https://www.google-analytics.com/mp/collect?measurement_id=XXX&api_secret=XXX`
- **Firebase**: `https://www.google-analytics.com/mp/collect?firebase_app_id=XXX&api_secret=XXX`

### 2. **Required IDs to Collect**

#### A. Firebase App ID
- **Purpose**: Unique identifier for your Firebase project
- **How to get**: 
  1. Go to Google Analytics Data Stream Details Page
  2. Copy the "Firebase ID"
- **Example**: `1:123456789:web:abcdef123456`

#### B. App Instance ID
- **Purpose**: Unique identifier for each app instance (user's device)
- **How to get**: Retrieved from frontend/mobile app
- **Frontend Code Example**:
```javascript
// Android/Kotlin example from article
private fun fetchFirebaseAppInstanceId() {
    FirebaseAnalytics.getInstance(this).appInstanceId.addOnCompleteListener {
        if (it.isSuccessful) {
            // Send this ID to your backend
            val appInstanceId = it.result
        }
    }
}
```

### 3. **Payload Structure Differences**

#### Current GA4 Format:
```json
{
  "client_id": "1234",
  "user_id": "user123",
  "events": [...]
}
```

#### Firebase Format (Required):
```json
{
  "app_instance_id": "XXXXXXXX",
  "user_id": "1234",
  "non_personalized_ads": false,
  "timestamp_micros": "1705494265000345",
  "events": [...]
}
```

### 4. **Event Enhancement Capabilities**

#### LTV Integration
- Add Lifetime Value (LTV) scores from Data Science models
- Example event with LTV:
```json
{
  "name": "deposit",
  "params": {
    "currency": "INR",
    "value": 1234,
    "ltvScore": "123.4"
  }
}
```

## 🛠️ Implementation Steps

### Step 1: Update Environment Variables
```bash
# Add to .env file
FIREBASE_APP_ID=1:123456789:web:abcdef123456
```

### Step 2: Frontend Integration
- Implement app instance ID collection
- Send app instance ID to backend on login/registration
- Store app instance ID per user in your database

### Step 3: Backend Processing
1. **Receive Events**: Capture user events (deposit, createTeam, etc.)
2. **Enrich Data**: Add LTV scores, session data, etc.
3. **Send to Firebase**: Use Firebase Analytics endpoint

### Step 4: Database Schema
```sql
-- Add to user table
ALTER TABLE users ADD COLUMN firebase_app_instance_id VARCHAR(255);
```

## 📊 Benefits Achieved (from Article)

1. **Accurate Event Counts**: Reduced 3x overcount to precise tracking
2. **Enhanced Campaign Optimization**: LTV data improves Google Ads targeting
3. **Better CAC**: More accurate Customer Acquisition Cost calculation
4. **Real-time Adjustments**: Campaigns respond faster to changes

## 🔧 Current Status

✅ **Completed**:
- Basic event sending structure
- Rate limiting with delays
- Debug endpoint integration

🔄 **In Progress**:
- Firebase endpoint integration
- Proper payload structure

❌ **Missing**:
- Firebase App ID collection
- App Instance ID from frontend
- LTV score integration
- User database updates

## 🚀 Next Steps

1. Get Firebase App ID from Google Analytics
2. Implement frontend app instance ID collection
3. Update user database to store app instance IDs
4. Integrate with Data Science LTV model
5. Test with Firebase debug endpoint
6. Monitor event accuracy in Firebase Analytics
