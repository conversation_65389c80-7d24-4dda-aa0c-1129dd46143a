const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
require("dotenv").config();

const MEASUREMENT_ID = process.env.MEASUREMENT_ID;
const API_SECRET = process.env.API_SECRET;
const ENDPOINT = `https://www.google-analytics.com/mp/collect?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;

// Generate a client_id (in real use, reuse per user/session)
const clientId = uuidv4();

async function sendPurchaseEvent() {
    console.log("hjesfdsfdswfs")
  const payload = {
    client_id: clientId,
    events: [
      {
        name: "purchase_success",
        params: {
          transaction_id: "ORDER_123",
          value: 999.99,
          currency: "INR",
          item_name: "Monthly Gym Plan",
          quantity: 1,
          platform: "backend",
        }
      }
    ]
  };

  try {
    const response = await axios.post(ENDPOINT, payload);
    console.log("res@@@@@@@" , response)
    console.log("✅ Event sent:", response.status); // should be 204
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);
  }
}

sendPurchaseEvent();
