const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
require("dotenv").config();

const MEASUREMENT_ID = process.env.MEASUREMENT_ID;
const API_SECRET = process.env.API_SECRET;
const ENDPOINT = `https://www.google-analytics.com/mp/collect?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;
const DEBUG_ENDPOINT = `https://www.google-analytics.com/debug/mp/collect?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;

// Generate a client_id (in real use, reuse per user/session)
const clientId = "1234_56789";

async function debugEvent() {
  const payload = {
    client_id: clientId,
    events: [
      {
        name: "purchase",
        params: {
          transaction_id: "ORDER_123",
          value: 999.99,
          currency: "INR",
          item_name: "Monthly Gym Plan",
          quantity: 1,
          platform: "backend",
          logged_in: true,
        },
      }
    ],
  };

  try {
    console.log("🔍 Debugging event...");
    const response = await axios.post(DEBUG_ENDPOINT, payload);
    console.log("Debug response:", JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error("❌ Debug Error:", error.response?.data || error.message);
  }
}

async function sendPurchaseEvent(val) {
  const payload = {
    client_id: clientId,
    events: [
      {
        name: "purchase",
        params: {
          transaction_id: "ORDER_123",
          value: val,
          currency: "INR",
          item_name: "Monthly Gym Plan",
          quantity: 1,
          platform: "backend",
          logged_in: true
        }
      }
    ]
  };

  try {
    const response = await axios.post(ENDPOINT, payload);
    console.log("✅ Event sent:", response.status); // should be 204
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);
  }
}

async function main() {
  // First debug the event to check for validation issues
  await debugEvent();

  // Then send the actual event
  for(let i=1;i<10;i++){
  await sendPurchaseEvent();
  }
}

main();
