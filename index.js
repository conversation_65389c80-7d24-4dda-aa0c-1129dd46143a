const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
const crypto = require("crypto");
require("dotenv").config();

const MEASUREMENT_ID = process.env.MEASUREMENT_ID;
const API_SECRET = process.env.API_SECRET;
const FIREBASE_APP_ID = process.env.FIREBASE_APP_ID; // New: Firebase App ID

// Google Analytics Measurement Protocol (current)
const GA_ENDPOINT = `https://www.google-analytics.com/mp/collect?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;
const GA_DEBUG_ENDPOINT = `https://www.google-analytics.com/debug/mp/collect?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;

// Firebase Analytics Measurement Protocol (from article)
// const FIREBASE_ENDPOINT = `https://www.google-analytics.com/mp/collect?firebase_app_id=${FIREBASE_APP_ID}&api_secret=${API_SECRET}`;
// const FIREBASE_DEBUG_ENDPOINT = `https://www.google-analytics.com/debug/mp/collect?firebase_app_id=${FIREBASE_APP_ID}&api_secret=${API_SECRET}`;

// Generate a client_id (in real use, reuse per user/session)
const clientId = "1234_56789";

// Firebase Analytics format (as per article)
async function sendFirebaseEvent(appInstanceId, userId, eventName, eventParams) {
  const payload = {
    app_instance_id: appInstanceId,
    user_id: userId,
    non_personalized_ads: false,
    timestamp_micros: (Date.now() * 1000).toString(), // Convert to microseconds
    events: [
      {
        name: eventName,
        params: eventParams
      }
    ]
  };

  try {
    const response = await axios.post(GA_ENDPOINT, payload);
    console.log(`✅ Firebase Event sent: ${response.status} | Event: ${eventName} | User: ${userId}`);
    return response;
  } catch (error) {
    console.error("❌ Firebase Error:", error.response?.data || error.message);
    throw error;
  }
}

// Debug Firebase event
async function debugFirebaseEvent() {
  const payload = {
    app_instance_id: "test_app_instance_123",
    user_id: "test_user_456",
    non_personalized_ads: false,
    timestamp_micros: (Date.now() * 1000).toString(),
    events: [
      {
        name: "deposit",
        params: {
          currency: "INR",
          value: 1234,
          ltvScore: "123.4"
        }
      }
    ]
  };

  try {
    console.log("🔍 Debugging Firebase event...");
    const response = await axios.post(FIREBASE_DEBUG_ENDPOINT, payload);
    console.log("Firebase Debug response:", JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error("❌ Firebase Debug Error:", error.response?.data || error.message);
  }
}

async function sendPurchaseEvent(val, clid) {
  const userId=clid;
  const sessionId = crypto.createHash('md5').update(userId + Date.now()).digest("hex");
  const payload = {
    client_id: clid,
    user_id: clid,
    events: [
      {
        name: "purchase",
        params: {
          transaction_id: clid,
          value: val,
          currency: "INR",
          item_name: "Monthly Gym Plan",
          quantity: 1,
          platform: "backend",
          logged_in: true,
          device_id: "hello_world",
          os:"android",
          session_id: sessionId,
    engagement_time_msec: 100,
    pack_type: "monthly"
        }
      }
    ]
  };

  try {
    const response = await axios.post(GA_ENDPOINT, payload);
    console.log(`✅ Event sent: ${response.status} | Value: ${val} | Client: ${clid}`);
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);
  }
}

// Helper function to add delay
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function main() {
  console.log("=== Testing Firebase Analytics Integration ===\n");

  // First debug the Firebase event to check for validation issues
  await debugFirebaseEvent();

  console.log("\n=== Sending Firebase events with delays ===");

  // Send Firebase events (as per article format)
  for(let i=1; i<6; i++){
    console.log(`\nSending Firebase event ${i}/5...`);

    // const appInstanceId = `app_instance_${Date.now()}_${i}`;
  const userId = `user_${i*34}`;

    await sendPurchaseEvent(100*i, userId);

    // Add 2 second delay between events to avoid rate limiting
    await delay(2000);
  }

  console.log("\n✅ All Firebase events sent!");
}

main();
