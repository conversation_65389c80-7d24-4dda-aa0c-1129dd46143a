const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
require("dotenv").config();

const MEASUREMENT_ID = process.env.MEASUREMENT_ID;
const API_SECRET = process.env.API_SECRET;
const ENDPOINT = `https://www.google-analytics.com/mp/collect?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;
const DEBUG_ENDPOINT = `https://www.google-analytics.com/debug/mp/collect?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;

// Generate a client_id (in real use, reuse per user/session)
const clientId = "1234_56789";

async function debugEvent() {
  const payload = {
    client_id: clientId,
    events: [
      {
        name: "purchase",
        params: {
          transaction_id: "ORDER_123",
          value: 999.99,
          currency: "INR",
          item_name: "Monthly Gym Plan",
          quantity: 1,
          platform: "backend",
          logged_in: true,
        },
      }
    ],
  };

  try {
    console.log("🔍 Debugging event...");
    const response = await axios.post(DEBUG_ENDPOINT, payload);
    console.log("Debug response:", JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error("❌ Debug Error:", error.response?.data || error.message);
  }
}

async function sendPurchaseEvent(val, clid) {
  const payload = {
    client_id: "1",
    user_id: "1234",
    events: [
      {
        name: "purchase",
        params: {
          transaction_id: clid,
          value: val,
          currency: "INR",
          item_name: "Monthly Gym Plan",
          quantity: 1,
          platform: "backend",
          logged_in: true,
              session_id": 123456789,
    "engagement_time_msec": 100

        }
      }
    ]
  };

  try {
    const response = await axios.post(ENDPOINT, payload);
    console.log(`✅ Event sent: ${response.status} | Value: ${val} | Client: ${clid}`);
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);
  }
}

// Helper function to add delay
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function main() {
  // First debug the event to check for validation issues
  await debugEvent();

  console.log("Sending events with delays to avoid rate limiting...");

  // Then send the actual events with delays
  for(let i=1; i<10; i++){
    console.log(`Sending event ${i}/9...`);
    await sendPurchaseEvent(i*100, `client_${Date.now()}_${i}`);

    // Add 1 second delay between events to avoid rate limiting
    await delay(1000);
  }

  console.log("All events sent!");
}

main();
